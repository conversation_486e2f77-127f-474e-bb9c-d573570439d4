import React from 'react';
import { Asset } from '../types';
import { <PERSON><PERSON>, Wrench, CheckCircle, AlertCircle } from 'lucide-react';

interface AssetBadgeProps {
  asset: Asset;
  size?: 'sm' | 'md' | 'lg';
  showStatus?: boolean;
}

const AssetBadge: React.FC<AssetBadgeProps> = ({ 
  asset, 
  size = 'md', 
  showStatus = true 
}) => {
  const getStatusColor = (status: Asset['status']) => {
    switch (status) {
      case 'available':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'reserved':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'maintenance':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusIcon = (status: Asset['status']) => {
    switch (status) {
      case 'available':
        return <CheckCircle className="w-3 h-3" />;
      case 'reserved':
        return <AlertCircle className="w-3 h-3" />;
      case 'maintenance':
        return <Wrench className="w-3 h-3" />;
      default:
        return <Cpu className="w-3 h-3" />;
    }
  };

  const sizeClasses = {
    sm: 'px-2 py-1 text-xs',
    md: 'px-3 py-1.5 text-sm',
    lg: 'px-4 py-2 text-base'
  };

  return (
    <div className={`
      inline-flex items-center gap-2 rounded-full border font-medium
      ${getStatusColor(asset.status)}
      ${sizeClasses[size]}
    `}>
      {showStatus && getStatusIcon(asset.status)}
      <span className="truncate">{asset.name}</span>
    </div>
  );
};

export default AssetBadge;