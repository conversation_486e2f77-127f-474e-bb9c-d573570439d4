import React from 'react';
import { AlertTriangle, Circle, ArrowUp } from 'lucide-react';

interface PriorityBadgeProps {
  priority: 'low' | 'medium' | 'high';
}

const PriorityBadge: React.FC<PriorityBadgeProps> = ({ priority }) => {
  const getPriorityConfig = (priority: 'low' | 'medium' | 'high') => {
    switch (priority) {
      case 'high':
        return {
          color: 'bg-red-100 text-red-800 border-red-200',
          icon: <ArrowUp className="w-3 h-3" />,
          label: 'High'
        };
      case 'medium':
        return {
          color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
          icon: <AlertTriangle className="w-3 h-3" />,
          label: 'Medium'
        };
      case 'low':
        return {
          color: 'bg-green-100 text-green-800 border-green-200',
          icon: <Circle className="w-3 h-3" />,
          label: 'Low'
        };
    }
  };

  const config = getPriorityConfig(priority);

  return (
    <div className={`
      inline-flex items-center gap-1.5 px-2 py-1 rounded-full border text-xs font-medium
      ${config.color}
    `}>
      {config.icon}
      <span>{config.label}</span>
    </div>
  );
};

export default PriorityBadge;