import React, { useState } from 'react';
import { KanbanBoard as KanbanBoardType, DataCard, Swimlane as SwimlaneType } from '../types';
import { Settings, Users, Bell, Search } from 'lucide-react';
import Swimlane from './Swimlane';
import AssetPanel from './AssetPanel';
import { useDragAndDrop } from '../hooks/useDragAndDrop';

interface KanbanBoardProps {
  board: KanbanBoardType;
  onUpdateBoard: (board: KanbanBoardType) => void;
}

const KanbanBoard: React.FC<KanbanBoardProps> = ({ board, onUpdateBoard }) => {
  const [showAssetPanel, setShowAssetPanel] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  
  const {
    draggedCard,
    dragOverSwimlane,
    handleDragStart,
    handleDragEnd,
    handleDragOver,
    handleDragLeave,
  } = useDragAndDrop();

  const handleCardMove = (swimlaneId: string) => {
    if (!draggedCard) return;

    const updatedCards = board.cards.map(card =>
      card.id === draggedCard.id
        ? { ...card, currentSwimlane: swimlaneId, updatedAt: new Date().toISOString() }
        : card
    );

    onUpdateBoard({
      ...board,
      cards: updatedCards
    });

    handleDragEnd();
  };

  const getCardsForSwimlane = (swimlaneId: string) => {
    return board.cards.filter(card => {
      const matchesSwimlane = card.currentSwimlane === swimlaneId;
      const matchesSearch = searchTerm === '' || 
        card.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        card.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        card.assets.some(asset => asset.name.toLowerCase().includes(searchTerm.toLowerCase()));
      
      return matchesSwimlane && matchesSearch;
    });
  };

  const sortedSwimlanes = [...board.swimlanes].sort((a, b) => a.order - b.order);

  return (
    <div className="h-screen bg-gray-50 flex flex-col">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <h1 className="text-2xl font-bold text-gray-900">{board.title}</h1>
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-500">
                {board.cards.length} cards
              </span>
              <span className="text-sm text-gray-500">•</span>
              <span className="text-sm text-gray-500">
                {board.assets.filter(a => a.status === 'available').length} available assets
              </span>
            </div>
          </div>

          <div className="flex items-center gap-3">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search cards..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent w-64"
              />
            </div>

            {/* Action Buttons */}
            <button className="p-2 hover:bg-gray-100 rounded-lg transition-colors">
              <Bell className="w-5 h-5 text-gray-500" />
            </button>
            <button className="p-2 hover:bg-gray-100 rounded-lg transition-colors">
              <Users className="w-5 h-5 text-gray-500" />
            </button>
            <button 
              onClick={() => setShowAssetPanel(!showAssetPanel)}
              className={`p-2 rounded-lg transition-colors ${
                showAssetPanel ? 'bg-blue-100 text-blue-600' : 'hover:bg-gray-100 text-gray-500'
              }`}
            >
              <Settings className="w-5 h-5" />
            </button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex overflow-hidden">
        {/* Kanban Board */}
        <div className="flex-1 overflow-x-auto">
          <div className="flex gap-6 p-6 h-full">
            {sortedSwimlanes.map((swimlane) => (
              <Swimlane
                key={swimlane.id}
                swimlane={swimlane}
                cards={getCardsForSwimlane(swimlane.id)}
                onDragStart={handleDragStart}
                onDragEnd={handleDragEnd}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                onDrop={handleCardMove}
                isDragOver={dragOverSwimlane === swimlane.id}
                draggedCard={draggedCard}
              />
            ))}
          </div>
        </div>

        {/* Asset Panel */}
        {showAssetPanel && (
          <AssetPanel
            assets={board.assets}
            onAssetSelect={(asset) => console.log('Selected asset:', asset)}
          />
        )}
      </div>
    </div>
  );
};

export default KanbanBoard;