import { useState, useCallback } from 'react';
import { DataCard } from '../types';

export const useDragAndDrop = () => {
  const [draggedCard, setDraggedCard] = useState<DataCard | null>(null);
  const [dragOverSwimlane, setDragOverSwimlane] = useState<string | null>(null);

  const handleDragStart = useCallback((card: DataCard) => {
    setDraggedCard(card);
  }, []);

  const handleDragEnd = useCallback(() => {
    setDraggedCard(null);
    setDragOverSwimlane(null);
  }, []);

  const handleDragOver = useCallback((swimlaneId: string) => {
    setDragOverSwimlane(swimlaneId);
  }, []);

  const handleDragLeave = useCallback(() => {
    setDragOverSwimlane(null);
  }, []);

  return {
    draggedCard,
    dragOverSwimlane,
    handleDragStart,
    handleDragEnd,
    handleDragOver,
    handleDragLeave,
  };
};