import React, { useState } from 'react';
import { TestStationCategory, TestStation, TestStationGroup } from '../types';
import { 
  ChevronDown, 
  ChevronRight, 
  Zap, 
  Shield, 
  Activity,
  ChevronLeft,
  Menu,
  Plus,
  Grid3X3,
  Layers,
  BarChart3
} from 'lucide-react';

interface TestStationSidebarProps {
  categories: TestStationCategory[];
  activeStationId?: string;
  activeGroupId?: string;
  onStationSelect: (station: TestStation) => void;
  onGroupSelect: (group: TestStationGroup) => void;
  onDashboardSelect: () => void;
  isCollapsed: boolean;
  onToggleCollapse: () => void;
}

const TestStationSidebar: React.FC<TestStationSidebarProps> = ({
  categories,
  activeStationId,
  activeGroupId,
  onStationSelect,
  onGroupSelect,
  onDashboardSelect,
  isCollapsed,
  onToggleCollapse
}) => {
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(
    new Set(['category-iec26', 'category-iec27'])
  );
  const [expandedGroups, setExpandedGroups] = useState<Set<string>>(new Set());

  const toggleCategory = (categoryId: string) => {
    const newExpanded = new Set(expandedCategories);
    if (newExpanded.has(categoryId)) {
      newExpanded.delete(categoryId);
    } else {
      newExpanded.add(categoryId);
    }
    setExpandedCategories(newExpanded);
  };

  const toggleGroup = (groupId: string) => {
    const newExpanded = new Set(expandedGroups);
    if (newExpanded.has(groupId)) {
      newExpanded.delete(groupId);
    } else {
      newExpanded.add(groupId);
    }
    setExpandedGroups(newExpanded);
  };

  const getCategoryIcon = (standard: string) => {
    switch (standard) {
      case 'IEC 60255-26':
        return <Zap className="w-4 h-4" />;
      case 'IEC 60255-27':
        return <Shield className="w-4 h-4" />;
      default:
        return <Activity className="w-4 h-4" />;
    }
  };

  const getStationStatusColor = (station: TestStation) => {
    if (station.isActive) {
      return 'bg-green-100 border-green-200';
    }
    return 'bg-gray-50 border-gray-200';
  };

  if (isCollapsed) {
    return (
      <div className="w-16 bg-white border-r border-gray-200 h-full flex flex-col">
        <div className="p-4 border-b border-gray-200">
          <button
            onClick={onToggleCollapse}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <Menu className="w-5 h-5 text-gray-600" />
          </button>
        </div>
        
        {/* Dashboard Button */}
        <div className="p-2">
          <button
            onClick={onDashboardSelect}
            className="w-full p-2 rounded-lg bg-blue-50 hover:bg-blue-100 transition-colors flex justify-center"
            title="Dashboard Overview"
          >
            <BarChart3 className="w-5 h-5 text-blue-600" />
          </button>
        </div>
        
        <div className="flex-1 p-2 space-y-2">
          {categories.map((category) => (
            <div key={category.id} className="space-y-1">
              <div className="p-2 rounded-lg bg-gray-50 flex justify-center">
                {getCategoryIcon(category.standard)}
              </div>
              {category.groups.map((group) => (
                <button
                  key={group.id}
                  onClick={() => onGroupSelect(group)}
                  className={`
                    w-full p-2 rounded-lg border transition-all duration-200 flex justify-center
                    ${group.id === activeGroupId 
                      ? 'bg-blue-100 border-blue-300 shadow-sm' 
                      : 'bg-gray-50 border-gray-200 hover:bg-gray-100'
                    }
                  `}
                  title={group.name}
                >
                  <Layers className="w-3 h-3" />
                </button>
              ))}
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="w-80 bg-white border-r border-gray-200 h-full overflow-hidden flex flex-col">
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between mb-2">
          <h2 className="text-lg font-semibold text-gray-900">Test Stations</h2>
          <div className="flex items-center gap-1">
            <button className="p-2 hover:bg-gray-100 rounded-lg transition-colors">
              <Plus className="w-4 h-4 text-gray-500" />
            </button>
            <button
              onClick={onToggleCollapse}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <ChevronLeft className="w-4 h-4 text-gray-500" />
            </button>
          </div>
        </div>
        <p className="text-sm text-gray-600">
          IEC 60255 Testing Standards
        </p>
      </div>

      {/* Dashboard Button */}
      <div className="p-4 border-b border-gray-100">
        <button
          onClick={onDashboardSelect}
          className="w-full p-3 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors flex items-center gap-3"
        >
          <div className="p-2 bg-blue-100 rounded-lg">
            <BarChart3 className="w-4 h-4 text-blue-600" />
          </div>
          <div className="text-left">
            <h3 className="font-medium text-blue-900">Dashboard Overview</h3>
            <p className="text-xs text-blue-600">View all stations status</p>
          </div>
        </button>
      </div>

      {/* Categories and Groups */}
      <div className="flex-1 overflow-y-auto">
        {categories.map((category) => (
          <div key={category.id} className="border-b border-gray-100 last:border-b-0">
            {/* Category Header */}
            <button
              onClick={() => toggleCategory(category.id)}
              className="w-full p-4 flex items-center justify-between hover:bg-gray-50 transition-colors"
            >
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-50 rounded-lg">
                  {getCategoryIcon(category.standard)}
                </div>
                <div className="text-left">
                  <h3 className="font-medium text-gray-900">{category.name}</h3>
                  <p className="text-xs text-gray-500">{category.standard}</p>
                </div>
              </div>
              {expandedCategories.has(category.id) ? (
                <ChevronDown className="w-4 h-4 text-gray-400" />
              ) : (
                <ChevronRight className="w-4 h-4 text-gray-400" />
              )}
            </button>

            {/* Groups List */}
            {expandedCategories.has(category.id) && (
              <div className="pb-2">
                {category.groups.map((group) => (
                  <div key={group.id} className="mx-2 mb-2">
                    {/* Group Header */}
                    <div className="flex items-center">
                      <button
                        onClick={() => onGroupSelect(group)}
                        className={`
                          flex-1 p-3 rounded-lg border text-left transition-all duration-200
                          ${group.id === activeGroupId 
                            ? 'bg-blue-50 border-blue-200 shadow-sm' 
                            : 'bg-gray-50 border-gray-200 hover:bg-gray-100 hover:border-gray-300'
                          }
                        `}
                      >
                        <div className="flex items-start justify-between mb-1">
                          <div className="flex items-center gap-2">
                            <Grid3X3 className="w-4 h-4 text-gray-500" />
                            <h4 className="font-medium text-gray-900 text-sm">
                              {group.name}
                            </h4>
                          </div>
                          <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                            {group.stations.length}
                          </span>
                        </div>
                        <p className="text-xs text-gray-600 line-clamp-2 ml-6">
                          {group.description}
                        </p>
                        <div className="flex items-center justify-between mt-2 ml-6">
                          <span className="text-xs text-gray-500">
                            {group.stations.reduce((acc, s) => acc + s.board.cards.length, 0)} total tests
                          </span>
                          <span className="text-xs text-gray-500">
                            {group.stations.filter(s => s.isActive).length} active
                          </span>
                        </div>
                      </button>
                      
                      <button
                        onClick={() => toggleGroup(group.id)}
                        className="ml-2 p-2 hover:bg-gray-100 rounded-lg transition-colors"
                      >
                        {expandedGroups.has(group.id) ? (
                          <ChevronDown className="w-4 h-4 text-gray-400" />
                        ) : (
                          <ChevronRight className="w-4 h-4 text-gray-400" />
                        )}
                      </button>
                    </div>

                    {/* Individual Stations in Group */}
                    {expandedGroups.has(group.id) && (
                      <div className="mt-2 ml-4 space-y-1">
                        {group.stations.map((station) => (
                          <button
                            key={station.id}
                            onClick={() => onStationSelect(station)}
                            className={`
                              w-full p-2 rounded-lg border text-left transition-all duration-200 text-sm
                              ${station.id === activeStationId 
                                ? 'bg-blue-50 border-blue-200 shadow-sm' 
                                : `${getStationStatusColor(station)} hover:bg-gray-100 hover:border-gray-300`
                              }
                            `}
                          >
                            <div className="flex items-center justify-between">
                              <span className="font-medium text-gray-900">
                                Station {station.stationNumber}
                              </span>
                              {station.isActive && (
                                <div className="w-2 h-2 bg-green-500 rounded-full" />
                              )}
                            </div>
                            <div className="flex items-center justify-between mt-1">
                              <span className="text-xs text-gray-500">
                                {station.board.cards.length} tests
                              </span>
                              <span className="text-xs text-gray-500">
                                {station.board.assets.filter(a => a.status === 'available').length} assets
                              </span>
                            </div>
                          </button>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Footer Stats */}
      <div className="p-4 border-t border-gray-200 bg-gray-50">
        <div className="grid grid-cols-2 gap-4 text-center">
          <div>
            <div className="text-lg font-semibold text-gray-900">
              {categories.reduce((acc, cat) => acc + cat.groups.length, 0)}
            </div>
            <div className="text-xs text-gray-600">Station Groups</div>
          </div>
          <div>
            <div className="text-lg font-semibold text-green-600">
              {categories.reduce((acc, cat) => 
                acc + cat.stations.filter(s => s.isActive).length, 0
              )}
            </div>
            <div className="text-xs text-gray-600">Active Stations</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestStationSidebar;