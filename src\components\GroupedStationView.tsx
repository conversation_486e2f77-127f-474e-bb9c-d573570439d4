import React, { useState } from 'react';
import { TestStationGroup, TestStation, KanbanBoard as KanbanBoardType } from '../types';
import { ChevronLeft, ChevronRight, Activity, Users, Settings } from 'lucide-react';
import KanbanBoard from './KanbanBoard';

interface GroupedStationViewProps {
  group: TestStationGroup;
  onUpdateBoard: (stationId: string, board: KanbanBoardType) => void;
  onBack: () => void;
}

const GroupedStationView: React.FC<GroupedStationViewProps> = ({
  group,
  onUpdateBoard,
  onBack
}) => {
  const [activeStationIndex, setActiveStationIndex] = useState(0);
  const [viewMode, setViewMode] = useState<'single' | 'grid'>('single');

  const activeStation = group.stations[activeStationIndex];

  const handleStationSelect = (index: number) => {
    setActiveStationIndex(index);
  };

  const handleBoardUpdate = (updatedBoard: KanbanBoardType) => {
    onUpdateBoard(activeStation.id, updatedBoard);
  };

  const getStationStats = (station: TestStation) => {
    const activeTests = station.board.cards.length;
    const availableAssets = station.board.assets.filter(a => a.status === 'available').length;
    const reservedAssets = station.board.assets.filter(a => a.status === 'reserved').length;
    
    return { activeTests, availableAssets, reservedAssets };
  };

  const getTotalStats = () => {
    return group.stations.reduce(
      (acc, station) => {
        const stats = getStationStats(station);
        return {
          activeTests: acc.activeTests + stats.activeTests,
          availableAssets: acc.availableAssets + stats.availableAssets,
          reservedAssets: acc.reservedAssets + stats.reservedAssets
        };
      },
      { activeTests: 0, availableAssets: 0, reservedAssets: 0 }
    );
  };

  const totalStats = getTotalStats();

  if (viewMode === 'grid') {
    return (
      <div className="h-screen bg-gray-50 flex flex-col">
        {/* Header */}
        <div className="bg-white border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <button
                onClick={onBack}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <ChevronLeft className="w-5 h-5 text-gray-500" />
              </button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">{group.name}</h1>
                <p className="text-sm text-gray-600">{group.standard} • {group.stations.length} stations</p>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <div className="flex items-center gap-4 text-sm text-gray-600">
                <span>{totalStats.activeTests} total tests</span>
                <span>{totalStats.availableAssets} available assets</span>
              </div>
              <button
                onClick={() => setViewMode('single')}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Single View
              </button>
            </div>
          </div>
        </div>

        {/* Grid View */}
        <div className="flex-1 overflow-auto p-6">
          <div className="grid grid-cols-1 xl:grid-cols-2 2xl:grid-cols-3 gap-6">
            {group.stations.map((station, index) => {
              const stats = getStationStats(station);
              return (
                <div key={station.id} className="bg-white rounded-lg border border-gray-200 overflow-hidden">
                  {/* Station Header */}
                  <div className="p-4 border-b border-gray-200 bg-gray-50">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="font-semibold text-gray-900">{station.name}</h3>
                      {station.isActive && (
                        <div className="w-2 h-2 bg-green-500 rounded-full" />
                      )}
                    </div>
                    <div className="flex items-center gap-4 text-xs text-gray-600">
                      <span>{stats.activeTests} tests</span>
                      <span>{stats.availableAssets} available</span>
                      <span>{stats.reservedAssets} reserved</span>
                    </div>
                  </div>

                  {/* Mini Kanban Preview */}
                  <div className="p-4 h-64 overflow-hidden">
                    <div className="flex gap-2 h-full">
                      {station.board.swimlanes.slice(0, 4).map((swimlane) => {
                        const cards = station.board.cards.filter(c => c.currentSwimlane === swimlane.id);
                        return (
                          <div key={swimlane.id} className="flex-1 min-w-0">
                            <div className="text-xs font-medium text-gray-700 mb-2 truncate">
                              {swimlane.title} ({cards.length})
                            </div>
                            <div className="space-y-1">
                              {cards.slice(0, 3).map((card) => (
                                <div
                                  key={card.id}
                                  className="p-2 bg-gray-50 rounded text-xs border truncate"
                                >
                                  {card.title}
                                </div>
                              ))}
                              {cards.length > 3 && (
                                <div className="text-xs text-gray-500 text-center">
                                  +{cards.length - 3} more
                                </div>
                              )}
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>

                  {/* Station Actions */}
                  <div className="p-4 border-t border-gray-200 bg-gray-50">
                    <button
                      onClick={() => {
                        setActiveStationIndex(index);
                        setViewMode('single');
                      }}
                      className="w-full px-3 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors"
                    >
                      Open Station
                    </button>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen bg-gray-50 flex flex-col">
      {/* Header with Station Tabs */}
      <div className="bg-white border-b border-gray-200">
        {/* Top Header */}
        <div className="px-6 py-4 border-b border-gray-100">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <button
                onClick={onBack}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <ChevronLeft className="w-5 h-5 text-gray-500" />
              </button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">{group.name}</h1>
                <p className="text-sm text-gray-600">{group.standard} • {group.stations.length} stations</p>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <div className="flex items-center gap-4 text-sm text-gray-600">
                <span>{totalStats.activeTests} total tests</span>
                <span>{totalStats.availableAssets} available assets</span>
              </div>
              <button
                onClick={() => setViewMode('grid')}
                className="px-4 py-2 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Grid View
              </button>
            </div>
          </div>
        </div>

        {/* Station Tabs */}
        <div className="px-6 overflow-x-auto">
          <div className="flex gap-1">
            {group.stations.map((station, index) => {
              const stats = getStationStats(station);
              const isActive = index === activeStationIndex;
              
              return (
                <button
                  key={station.id}
                  onClick={() => handleStationSelect(index)}
                  className={`
                    flex-shrink-0 px-4 py-3 border-b-2 transition-all duration-200
                    ${isActive 
                      ? 'border-blue-500 bg-blue-50 text-blue-700' 
                      : 'border-transparent hover:bg-gray-50 text-gray-600'
                    }
                  `}
                >
                  <div className="flex items-center gap-2">
                    <div className="text-left">
                      <div className="font-medium text-sm">Station {station.stationNumber}</div>
                      <div className="text-xs opacity-75">
                        {stats.activeTests} tests • {stats.availableAssets} available
                      </div>
                    </div>
                    {station.isActive && (
                      <div className="w-2 h-2 bg-green-500 rounded-full" />
                    )}
                  </div>
                </button>
              );
            })}
          </div>
        </div>
      </div>

      {/* Active Station Content */}
      <div className="flex-1 overflow-hidden">
        <KanbanBoard
          board={activeStation.board}
          onUpdateBoard={handleBoardUpdate}
        />
      </div>
    </div>
  );
};

export default GroupedStationView;