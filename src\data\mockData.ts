import { KanbanBoard, Asset, DataCard, Swimlane, User } from '../types';

export const mockAssets: Asset[] = [
  {
    id: 'asset-001',
    name: 'EMC Test Chamber',
    type: 'Testing Equipment',
    status: 'available',
    description: 'Anechoic chamber for radiated immunity testing',
    location: 'EMC Lab A'
  },
  {
    id: 'asset-002',
    name: 'Signal Generator 9kHz-3GHz',
    type: 'RF Equipment',
    status: 'available',
    description: 'High-frequency signal generator for EMC testing',
    location: 'EMC Lab A'
  },
  {
    id: 'asset-003',
    name: 'Power Amplifier 1GHz',
    type: 'RF Equipment',
    status: 'available',
    description: '100W power amplifier for immunity testing',
    location: 'EMC Lab A'
  },
  {
    id: 'asset-004',
    name: 'ESD Generator',
    type: 'Testing Equipment',
    status: 'maintenance',
    description: 'Electrostatic discharge test generator',
    location: 'EMC Lab B'
  },
  {
    id: 'asset-005',
    name: 'Coupling/Decoupling Network',
    type: 'Testing Equipment',
    status: 'available',
    description: 'CDN for conducted immunity testing',
    location: 'EMC Lab B'
  },
  {
    id: 'asset-006',
    name: 'High Voltage Tester',
    type: 'Safety Equipment',
    status: 'available',
    description: 'Dielectric strength test equipment',
    location: 'Safety Lab'
  },
  {
    id: 'asset-007',
    name: 'Insulation Tester',
    type: 'Safety Equipment',
    status: 'available',
    description: 'Insulation resistance measurement',
    location: 'Safety Lab'
  }
];

export const mockSwimlanes: Swimlane[] = [
  {
    id: 'swimlane-001',
    title: 'Test Planning',
    color: '#6B7280',
    order: 0
  },
  {
    id: 'swimlane-002',
    title: 'Equipment Reserved',
    color: '#F59E0B',
    order: 1
  },
  {
    id: 'swimlane-003',
    title: 'Testing In Progress',
    color: '#3B82F6',
    order: 2
  },
  {
    id: 'swimlane-004',
    title: 'Test Complete',
    color: '#10B981',
    order: 3
  },
  {
    id: 'swimlane-005',
    title: 'Report Review',
    color: '#8B5CF6',
    order: 4
  }
];

export const mockUsers: User[] = [
  {
    id: 'user-001',
    name: 'John Smith',
    email: '<EMAIL>',
    role: 'admin'
  },
  {
    id: 'user-002',
    name: 'Sarah Johnson',
    email: '<EMAIL>',
    role: 'user'
  },
  {
    id: 'user-003',
    name: 'Mike Chen',
    email: '<EMAIL>',
    role: 'user'
  }
];

export const mockDataCards: DataCard[] = [
  {
    id: 'card-001',
    title: 'Radiated Immunity - Relay Protection Unit',
    description: 'IEC 60255-26 radiated immunity testing for new relay protection unit',
    assets: [mockAssets[0], mockAssets[1], mockAssets[2]],
    createdBy: 'user-001',
    createdAt: '2024-01-15T09:30:00Z',
    updatedAt: '2024-01-15T09:30:00Z',
    currentSwimlane: 'swimlane-002',
    priority: 'high',
    dueDate: '2024-01-25T17:00:00Z',
    tags: ['IEC-60255-26', 'radiated-immunity', 'relay-protection']
  },
  {
    id: 'card-002',
    title: 'ESD Testing - Control Interface',
    description: 'Electrostatic discharge immunity testing on control interface',
    assets: [mockAssets[3]],
    createdBy: 'user-002',
    createdAt: '2024-01-16T14:15:00Z',
    updatedAt: '2024-01-16T14:15:00Z',
    currentSwimlane: 'swimlane-001',
    priority: 'medium',
    dueDate: '2024-01-30T17:00:00Z',
    tags: ['IEC-60255-26', 'esd', 'control-interface']
  },
  {
    id: 'card-003',
    title: 'Conducted Immunity - Power Supply',
    description: 'RF conducted immunity testing on power supply circuits',
    assets: [mockAssets[4]],
    createdBy: 'user-003',
    createdAt: '2024-01-17T11:00:00Z',
    updatedAt: '2024-01-17T11:00:00Z',
    currentSwimlane: 'swimlane-003',
    priority: 'medium',
    tags: ['IEC-60255-26', 'conducted-immunity', 'power-supply']
  }
];

export const mockKanbanBoard: KanbanBoard = {
  id: 'board-001',
  title: 'Test Station Asset Tracker',
  swimlanes: mockSwimlanes,
  cards: mockDataCards,
  assets: mockAssets,
  users: mockUsers
};