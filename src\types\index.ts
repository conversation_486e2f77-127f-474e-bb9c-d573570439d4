export interface Asset {
  id: string;
  name: string;
  type: string;
  status: 'available' | 'reserved' | 'maintenance';
  description?: string;
  location?: string;
}

export interface DataCard {
  id: string;
  title: string;
  description?: string;
  assets: Asset[];
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  currentSwimlane: string;
  priority: 'low' | 'medium' | 'high';
  dueDate?: string;
  tags?: string[];
}

export interface Swimlane {
  id: string;
  title: string;
  color: string;
  order: number;
  cardLimit?: number;
}

export interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  role: 'admin' | 'user' | 'viewer';
}

export interface KanbanBoard {
  id: string;
  title: string;
  swimlanes: Swimlane[];
  cards: DataCard[];
  assets: Asset[];
  users: User[];
}

export interface TestStation {
  id: string;
  name: string;
  standard: string;
  category: 'IEC 60255-26' | 'IEC 60255-27';
  description: string;
  board: KanbanBoard;
  isActive?: boolean;
  stationNumber?: number; // For grouped stations (e.g., Station 1, Station 2, etc.)
  groupId?: string; // Links stations of the same type
}

export interface TestStationGroup {
  id: string;
  name: string;
  standard: string;
  category: 'IEC 60255-26' | 'IEC 60255-27';
  description: string;
  stations: TestStation[];
  isGrouped: boolean;
}

export interface TestStationCategory {
  id: string;
  name: string;
  standard: 'IEC 60255-26' | 'IEC 60255-27';
  description: string;
  stations: TestStation[];
  groups: TestStationGroup[];
}