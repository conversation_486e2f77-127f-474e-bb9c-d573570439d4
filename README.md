# Test Station Kanban Tracker 📋

A modern web application for managing test station assets and workflows using an intuitive kanban board interface. Built for teams working with IEC 60255-26 (EMC Testing) and IEC 60255-27 (Product Safety) standards.

## 🎯 What This App Does

Think of this like a digital whiteboard where you can:
- **Track test equipment** (like expensive lab instruments)
- **Manage test workflows** (from planning to completion)
- **See everything at a glance** (dashboard overview)
- **Organize by test types** (EMC testing, safety testing, etc.)

## 🚀 Quick Start

### Prerequisites
- **Node.js** (version 18 or higher) - [Download here](https://nodejs.org/)
- **npm** (comes with Node.js)
- A code editor like **VS Code** - [Download here](https://code.visualstudio.com/)

### Installation
```bash
# 1. Clone or download this project
# 2. Open terminal in the project folder
# 3. Install dependencies
npm install

# 4. Start the development server
npm run dev

# 5. Open your browser to http://localhost:5173
```

## 🏗️ Project Structure

```
test-station-tracker/
├── src/                          # All source code lives here
│   ├── components/               # Reusable UI pieces
│   │   ├── KanbanBoard.tsx      # Main kanban board view
│   │   ├── TestStationSidebar.tsx # Left navigation panel
│   │   ├── TestStationDashboard.tsx # Overview page
│   │   ├── DataCard.tsx         # Individual test cards
│   │   ├── Swimlane.tsx         # Kanban columns
│   │   ├── AssetPanel.tsx       # Equipment management panel
│   │   └── ...                  # Other UI components
│   ├── data/                    # Sample data and configuration
│   │   ├── testStations.ts      # Test station definitions
│   │   └── mockData.ts          # Sample cards and assets
│   ├── hooks/                   # Custom React functionality
│   │   └── useDragAndDrop.ts    # Drag and drop logic
│   ├── types/                   # TypeScript definitions
│   │   └── index.ts             # Data structure definitions
│   ├── App.tsx                  # Main application component
│   └── main.tsx                 # Application entry point
├── public/                      # Static files
├── package.json                 # Project dependencies and scripts
└── README.md                    # This file!
```

## 🎨 Key Features

### 1. **Dashboard Overview** 📊
- See all test stations at once
- Quick status indicators (pending, in progress, completed)
- Asset availability counts
- Filter by test category or search by name

### 2. **Kanban Board Interface** 📋
- Drag and drop test cards between workflow stages
- Visual workflow: Planning → Reserved → Testing → Complete → Review
- Asset assignment and tracking
- Search and filter capabilities

### 3. **Test Station Management** 🏭
- Organized by IEC standards (60255-26 and 60255-27)
- Grouped stations (e.g., 5 identical mechanical stress stations)
- Individual station views
- Multi-station group views

### 4. **Asset Tracking** 🔧
- Equipment status (available, reserved, maintenance)
- Asset assignment to test cards
- Real-time availability tracking
- Equipment location tracking

## 🛠️ Technology Stack

- **React 18** - User interface framework
- **TypeScript** - Type-safe JavaScript
- **Tailwind CSS** - Styling and design
- **Vite** - Fast development server
- **Lucide React** - Beautiful icons

## 📱 How to Use

### Basic Workflow
1. **Start at Dashboard** - See overview of all stations
2. **Select a Station** - Click any station to view its kanban board
3. **Manage Tests** - Drag cards between workflow stages
4. **Track Assets** - Use the asset panel to manage equipment
5. **Monitor Progress** - Return to dashboard for status updates

### Navigation
- **Sidebar** - Switch between stations and groups
- **Dashboard Button** - Return to overview at any time
- **Collapse Sidebar** - More screen space for kanban boards
- **Search & Filter** - Find specific stations or tests quickly

## 🎓 For Beginners

### What is a Kanban Board?
A kanban board is like a digital version of sticky notes on a whiteboard. Each "card" represents a task (in our case, a test), and columns represent different stages of work (planning, in progress, done, etc.).

### What are Components?
Think of components like LEGO blocks - small, reusable pieces that you combine to build the full application. Each `.tsx` file in the `components/` folder is one building block.

### What is TypeScript?
TypeScript is JavaScript with extra rules that help prevent bugs. The `.ts` and `.tsx` files define what kind of data each part of the app expects, making it more reliable.

## 🔧 Common Tasks

### Adding a New Test Station
1. Open `src/data/testStations.ts`
2. Find the appropriate category (IEC 60255-26 or 60255-27)
3. Add your station to the existing array or create a new group
4. The app will automatically include it in the sidebar and dashboard

### Modifying Workflow Stages
1. Open `src/data/mockData.ts`
2. Edit the `mockSwimlanes` array
3. Add, remove, or rename stages as needed
4. Update the `order` property to control column sequence

### Changing Colors and Styling
1. This app uses Tailwind CSS classes for styling
2. Look for `className` attributes in component files
3. Common patterns:
   - `bg-blue-500` = blue background
   - `text-gray-900` = dark gray text
   - `p-4` = padding
   - `rounded-lg` = rounded corners

## 🐛 Troubleshooting

### App Won't Start
```bash
# Try these commands in order:
npm install          # Reinstall dependencies
npm run dev         # Start development server
```

### Changes Not Showing
- Save your files (Ctrl+S or Cmd+S)
- Check the terminal for error messages
- Refresh your browser (F5)

### TypeScript Errors
- Red squiggly lines usually mean type mismatches
- Check that your data matches the expected structure
- Look at `src/types/index.ts` for data structure definitions

## 📚 Learning Resources

### React Basics
- [React Official Tutorial](https://react.dev/learn)
- [React Components Explained](https://react.dev/learn/your-first-component)

### TypeScript
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [TypeScript for React](https://react-typescript-cheatsheet.netlify.app/)

### Tailwind CSS
- [Tailwind Documentation](https://tailwindcss.com/docs)
- [Tailwind Cheat Sheet](https://tailwindcomponents.com/cheatsheet/)

## 🤝 Contributing

1. Make your changes in a new branch
2. Test thoroughly
3. Update documentation if needed
4. Submit a pull request with clear description

## 📄 License

This project is open source and available under the MIT License.

---

**Need Help?** Check the PLAYBOOK.md file for detailed step-by-step instructions on common tasks!