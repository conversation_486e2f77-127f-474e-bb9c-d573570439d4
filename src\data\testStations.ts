import { TestStationCategory, TestStation, TestStationGroup, KanbanBoard } from '../types';
import { mockSwimlanes, mockUsers } from './mockData';

// Create base board template
const createBaseBoard = (id: string, title: string): KanbanBoard => ({
  id,
  title,
  swimlanes: mockSwimlanes,
  cards: [],
  assets: [],
  users: mockUsers
});

// Helper function to create grouped stations
const createGroupedStations = (
  baseId: string,
  baseName: string,
  standard: string,
  category: 'IEC 60255-26' | 'IEC 60255-27',
  description: string,
  count: number,
  groupId: string
): TestStation[] => {
  return Array.from({ length: count }, (_, index) => ({
    id: `${baseId}-${String(index + 1).padStart(3, '0')}`,
    name: `${baseName} - Station ${index + 1}`,
    standard,
    category,
    description: `${description} (Station ${index + 1})`,
    board: createBaseBoard(`board-${baseId}-${index + 1}`, `${baseName} - Station ${index + 1}`),
    isActive: index === 0, // First station active by default
    stationNumber: index + 1,
    groupId
  }));
};

// IEC 60255-26 EMC Testing Stations
const radiatedImmunityStations = createGroupedStations(
  'station-iec26-radiated',
  'Radiated Immunity Test',
  'IEC 60255-26',
  'IEC 60255-26',
  'Testing electromagnetic field immunity (80 MHz to 1 GHz)',
  3,
  'group-radiated-immunity'
);

const conductedImmunityStations = createGroupedStations(
  'station-iec26-conducted',
  'Conducted Immunity Test',
  'IEC 60255-26',
  'IEC 60255-26',
  'RF conducted immunity testing (150 kHz to 80 MHz)',
  2,
  'group-conducted-immunity'
);

const esdImmunityStations = createGroupedStations(
  'station-iec26-esd',
  'ESD Immunity Test',
  'IEC 60255-26',
  'IEC 60255-26',
  'Electrostatic discharge immunity testing',
  4,
  'group-esd-immunity'
);

const surgeImmunityStations = createGroupedStations(
  'station-iec26-surge',
  'Surge Immunity Test',
  'IEC 60255-26',
  'IEC 60255-26',
  'Surge immunity testing for power and signal lines',
  2,
  'group-surge-immunity'
);

// IEC 60255-27 Product Safety Stations
const mechanicalStressStations = createGroupedStations(
  'station-iec27-mechanical',
  'Mechanical Stress Test',
  'IEC 60255-27',
  'IEC 60255-27',
  'Mechanical stress and vibration testing',
  5,
  'group-mechanical-stress'
);

const temperatureRiseStations = createGroupedStations(
  'station-iec27-temperature',
  'Temperature Rise Test',
  'IEC 60255-27',
  'IEC 60255-27',
  'Temperature rise and thermal testing',
  3,
  'group-temperature-rise'
);

const dielectricStrengthStations = createGroupedStations(
  'station-iec27-dielectric',
  'Dielectric Strength Test',
  'IEC 60255-27',
  'IEC 60255-27',
  'High voltage dielectric strength testing',
  2,
  'group-dielectric-strength'
);

// Create station groups
const iec60255_26_groups: TestStationGroup[] = [
  {
    id: 'group-radiated-immunity',
    name: 'Radiated Immunity Test',
    standard: 'IEC 60255-26',
    category: 'IEC 60255-26',
    description: 'Testing electromagnetic field immunity (80 MHz to 1 GHz)',
    stations: radiatedImmunityStations,
    isGrouped: true
  },
  {
    id: 'group-conducted-immunity',
    name: 'Conducted Immunity Test',
    standard: 'IEC 60255-26',
    category: 'IEC 60255-26',
    description: 'RF conducted immunity testing (150 kHz to 80 MHz)',
    stations: conductedImmunityStations,
    isGrouped: true
  },
  {
    id: 'group-esd-immunity',
    name: 'ESD Immunity Test',
    standard: 'IEC 60255-26',
    category: 'IEC 60255-26',
    description: 'Electrostatic discharge immunity testing',
    stations: esdImmunityStations,
    isGrouped: true
  },
  {
    id: 'group-surge-immunity',
    name: 'Surge Immunity Test',
    standard: 'IEC 60255-26',
    category: 'IEC 60255-26',
    description: 'Surge immunity testing for power and signal lines',
    stations: surgeImmunityStations,
    isGrouped: true
  }
];

const iec60255_27_groups: TestStationGroup[] = [
  {
    id: 'group-mechanical-stress',
    name: 'Mechanical Stress Test',
    standard: 'IEC 60255-27',
    category: 'IEC 60255-27',
    description: 'Mechanical stress and vibration testing',
    stations: mechanicalStressStations,
    isGrouped: true
  },
  {
    id: 'group-temperature-rise',
    name: 'Temperature Rise Test',
    standard: 'IEC 60255-27',
    category: 'IEC 60255-27',
    description: 'Temperature rise and thermal testing',
    stations: temperatureRiseStations,
    isGrouped: true
  },
  {
    id: 'group-dielectric-strength',
    name: 'Dielectric Strength Test',
    standard: 'IEC 60255-27',
    category: 'IEC 60255-27',
    description: 'High voltage dielectric strength testing',
    stations: dielectricStrengthStations,
    isGrouped: true
  }
];

export const testStationCategories: TestStationCategory[] = [
  {
    id: 'category-iec26',
    name: 'EMC Testing',
    standard: 'IEC 60255-26',
    description: 'Electromagnetic compatibility testing for protection equipment',
    stations: [...radiatedImmunityStations, ...conductedImmunityStations, ...esdImmunityStations, ...surgeImmunityStations],
    groups: iec60255_26_groups
  },
  {
    id: 'category-iec27',
    name: 'Product Safety',
    standard: 'IEC 60255-27',
    description: 'Product safety requirements for protection equipment',
    stations: [...mechanicalStressStations, ...temperatureRiseStations, ...dielectricStrengthStations],
    groups: iec60255_27_groups
  }
];

export const getAllTestStations = (): TestStation[] => {
  return testStationCategories.flatMap(category => category.stations);
};

export const getTestStationById = (id: string): TestStation | undefined => {
  return getAllTestStations().find(station => station.id === id);
};

export const getTestStationGroupById = (groupId: string): TestStationGroup | undefined => {
  return testStationCategories
    .flatMap(category => category.groups)
    .find(group => group.id === groupId);
};

export const getAllTestStationGroups = (): TestStationGroup[] => {
  return testStationCategories.flatMap(category => category.groups);
};