import React, { useState } from 'react';
import KanbanBoard from './components/KanbanBoard';
import TestStationSidebar from './components/TestStationSidebar';
import GroupedStationView from './components/GroupedStationView';
import TestStationDashboard from './components/TestStationDashboard';
import { testStationCategories, getTestStationById, getTestStationGroupById } from './data/testStations';
import { TestStation, TestStationGroup, KanbanBoard as KanbanBoardType } from './types';

type ViewMode = 'dashboard' | 'single-station' | 'grouped-stations';

function App() {
  const [viewMode, setViewMode] = useState<ViewMode>('dashboard');
  const [activeStationId, setActiveStationId] = useState<string>('station-iec26-radiated-001');
  const [activeGroupId, setActiveGroupId] = useState<string | undefined>();
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  
  const activeStation = getTestStationById(activeStationId);
  const activeGroup = activeGroupId ? getTestStationGroupById(activeGroupId) : undefined;

  const handleStationSelect = (station: TestStation) => {
    setActiveStationId(station.id);
    setActiveGroupId(undefined);
    setViewMode('single-station');
  };

  const handleGroupSelect = (group: TestStationGroup) => {
    setActiveGroupId(group.id);
    setActiveStationId('');
    setViewMode('grouped-stations');
  };

  const handleDashboardSelect = () => {
    setViewMode('dashboard');
    setActiveGroupId(undefined);
    setActiveStationId('');
  };

  const handleUpdateBoard = (updatedBoard: KanbanBoardType) => {
    // In a real application, this would update the station's board in your data store
    console.log('Board updated for station:', activeStationId, updatedBoard);
  };

  const handleUpdateGroupBoard = (stationId: string, updatedBoard: KanbanBoardType) => {
    // In a real application, this would update the specific station's board in the group
    console.log('Board updated for station in group:', stationId, updatedBoard);
  };

  const toggleSidebar = () => {
    setIsSidebarCollapsed(!isSidebarCollapsed);
  };

  const handleBackToSidebar = () => {
    setViewMode('dashboard');
    setActiveGroupId(undefined);
    setActiveStationId('');
  };

  // Render dashboard view
  if (viewMode === 'dashboard') {
    return (
      <div className="h-screen bg-gray-50 flex">
        {/* Test Station Sidebar */}
        <TestStationSidebar
          categories={testStationCategories}
          activeStationId={activeStationId}
          activeGroupId={activeGroupId}
          onStationSelect={handleStationSelect}
          onGroupSelect={handleGroupSelect}
          onDashboardSelect={handleDashboardSelect}
          isCollapsed={isSidebarCollapsed}
          onToggleCollapse={toggleSidebar}
        />

        {/* Dashboard Content */}
        <div className="flex-1 flex flex-col overflow-hidden">
          <TestStationDashboard
            categories={testStationCategories}
            onStationSelect={handleStationSelect}
            onGroupSelect={handleGroupSelect}
            onBack={handleBackToSidebar}
          />
        </div>
      </div>
    );
  }

  // Render grouped station view
  if (viewMode === 'grouped-stations' && activeGroup) {
    return (
      <div className="h-screen bg-gray-50 flex">
        {/* Test Station Sidebar */}
        <TestStationSidebar
          categories={testStationCategories}
          activeStationId={activeStationId}
          activeGroupId={activeGroupId}
          onStationSelect={handleStationSelect}
          onGroupSelect={handleGroupSelect}
          onDashboardSelect={handleDashboardSelect}
          isCollapsed={isSidebarCollapsed}
          onToggleCollapse={toggleSidebar}
        />

        {/* Grouped Station View */}
        <div className="flex-1 flex flex-col overflow-hidden">
          <GroupedStationView
            group={activeGroup}
            onUpdateBoard={handleUpdateGroupBoard}
            onBack={handleBackToSidebar}
          />
        </div>
      </div>
    );
  }

  // Render single station view
  if (!activeStation) {
    return (
      <div className="h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Test Station Not Found
          </h2>
          <p className="text-gray-600">
            The selected test station could not be loaded.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen bg-gray-50 flex">
      {/* Test Station Sidebar */}
      <TestStationSidebar
        categories={testStationCategories}
        activeStationId={activeStationId}
        activeGroupId={activeGroupId}
        onStationSelect={handleStationSelect}
        onGroupSelect={handleGroupSelect}
        onDashboardSelect={handleDashboardSelect}
        isCollapsed={isSidebarCollapsed}
        onToggleCollapse={toggleSidebar}
      />

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        <KanbanBoard 
          board={activeStation.board} 
          onUpdateBoard={handleUpdateBoard}
        />
      </div>
    </div>
  );
}

export default App;