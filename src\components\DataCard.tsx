import React from 'react';
import { DataCard as DataCardType } from '../types';
import { Calendar, User, MoreHorizontal } from 'lucide-react';
import AssetBadge from './AssetBadge';
import PriorityBadge from './PriorityBadge';

interface DataCardProps {
  card: DataCardType;
  onDragStart: (card: DataCardType) => void;
  onDragEnd: () => void;
  isDragging?: boolean;
}

const DataCard: React.FC<DataCardProps> = ({ 
  card, 
  onDragStart, 
  onDragEnd, 
  isDragging = false 
}) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    });
  };

  const handleDragStart = (e: React.DragEvent) => {
    e.dataTransfer.effectAllowed = 'move';
    e.dataTransfer.setData('text/plain', card.id);
    onDragStart(card);
  };

  return (
    <div
      draggable
      onDragStart={handleDragStart}
      onDragEnd={onDragEnd}
      className={`
        bg-white rounded-lg shadow-sm border border-gray-200 p-4 cursor-move
        transition-all duration-200 hover:shadow-md hover:border-gray-300
        ${isDragging ? 'opacity-50 rotate-2 scale-105' : ''}
      `}
    >
      {/* Header */}
      <div className="flex items-start justify-between mb-3">
        <h3 className="font-semibold text-gray-900 text-sm line-clamp-2">
          {card.title}
        </h3>
        <button className="p-1 hover:bg-gray-100 rounded transition-colors">
          <MoreHorizontal className="w-4 h-4 text-gray-400" />
        </button>
      </div>

      {/* Description */}
      {card.description && (
        <p className="text-gray-600 text-xs mb-3 line-clamp-2">
          {card.description}
        </p>
      )}

      {/* Assets */}
      <div className="space-y-2 mb-3">
        {card.assets.map((asset) => (
          <AssetBadge key={asset.id} asset={asset} size="sm" />
        ))}
      </div>

      {/* Tags */}
      {card.tags && card.tags.length > 0 && (
        <div className="flex flex-wrap gap-1 mb-3">
          {card.tags.map((tag) => (
            <span
              key={tag}
              className="px-2 py-1 bg-blue-50 text-blue-700 text-xs rounded-full"
            >
              {tag}
            </span>
          ))}
        </div>
      )}

      {/* Footer */}
      <div className="flex items-center justify-between pt-3 border-t border-gray-100">
        <div className="flex items-center gap-2">
          <PriorityBadge priority={card.priority} />
          {card.dueDate && (
            <div className="flex items-center gap-1 text-xs text-gray-500">
              <Calendar className="w-3 h-3" />
              <span>{formatDate(card.dueDate)}</span>
            </div>
          )}
        </div>
        
        <div className="flex items-center gap-1 text-xs text-gray-500">
          <User className="w-3 h-3" />
          <span>{card.createdBy}</span>
        </div>
      </div>
    </div>
  );
};

export default DataCard;