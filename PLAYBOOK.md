# Test Station Kanban Tracker - PLAYBOOK 🎮

*A step-by-step guide for college freshmen and beginners*

## 📖 Table of Contents
1. [Understanding the App](#understanding-the-app)
2. [File Structure Deep Dive](#file-structure-deep-dive)
3. [How to Add New Features](#how-to-add-new-features)
4. [How to Modify Existing Features](#how-to-modify-existing-features)
5. [Common Customizations](#common-customizations)
6. [Debugging Guide](#debugging-guide)
7. [Deployment Guide](#deployment-guide)

---

## 🎯 Understanding the App

### The Big Picture
Imagine you're managing a testing laboratory with expensive equipment. You need to:
- **Track which equipment is available** (like a library checkout system)
- **Manage test workflows** (from planning to completion)
- **See everything at once** (like a mission control dashboard)

### Key Concepts

#### 1. **Test Stations** 🏭
Think of these as different testing labs:
- **EMC Lab** (electromagnetic compatibility testing)
- **Safety Lab** (product safety testing)
- Each lab has specific equipment and procedures

#### 2. **Kanban Boards** 📋
Like a digital whiteboard with columns:
- **Planning** - Tests being planned
- **Reserved** - Equipment is booked
- **Testing** - Tests currently running
- **Complete** - Tests finished
- **Review** - Reports being reviewed

#### 3. **Data Cards** 🎴
Each card represents one test session:
- Contains test details
- Lists required equipment
- Shows who's responsible
- Tracks progress through workflow

#### 4. **Assets** 🔧
The expensive equipment:
- Signal generators, test chambers, etc.
- Can be available, reserved, or in maintenance
- Tracked by location and status

---

## 📁 File Structure Deep Dive

### Core Application Files

#### `src/App.tsx` - The Main Controller
```typescript
// This is like the "brain" of your app
// It decides what to show based on user actions

function App() {
  // State variables - these remember what's currently selected
  const [viewMode, setViewMode] = useState<ViewMode>('dashboard');
  const [activeStationId, setActiveStationId] = useState<string>('...');
  
  // Event handlers - these respond to user clicks
  const handleStationSelect = (station: TestStation) => {
    // When user clicks a station, switch to that view
  };
}
```

**What it does:**
- Manages which view is currently shown (dashboard, single station, or group)
- Handles navigation between different parts of the app
- Passes data down to child components

**When to modify:**
- Adding new main views or pages
- Changing navigation logic
- Adding global state management

#### `src/main.tsx` - The Starting Point
```typescript
// This file starts your entire app
// Think of it as the "power button"

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <App />
  </StrictMode>
);
```

**What it does:**
- Connects your React app to the HTML page
- Enables development tools and error checking

**When to modify:**
- Almost never! This is boilerplate code
- Only change if adding global providers (like authentication)

### Component Files (UI Building Blocks)

#### `src/components/TestStationDashboard.tsx` - The Overview Page
```typescript
// This shows the "mission control" view
// Like looking at all your labs from above

interface TestStationDashboardProps {
  categories: TestStationCategory[];  // All your test station data
  onStationSelect: (station: TestStation) => void;  // What to do when clicked
}
```

**What it does:**
- Shows summary statistics for all stations
- Provides search and filtering
- Offers both card and table views

**When to modify:**
- Changing what statistics are displayed
- Adding new filter options
- Modifying the layout or design

#### `src/components/KanbanBoard.tsx` - The Workflow View
```typescript
// This is the main "work area" where tests are managed
// Like a digital whiteboard with sticky notes

const KanbanBoard: React.FC<KanbanBoardProps> = ({ board, onUpdateBoard }) => {
  // Drag and drop functionality
  const { draggedCard, handleDragStart, handleDragEnd } = useDragAndDrop();
  
  // Filter cards based on search
  const getCardsForSwimlane = (swimlaneId: string) => {
    return board.cards.filter(card => /* filtering logic */);
  };
}
```

**What it does:**
- Displays workflow columns (swimlanes)
- Handles drag-and-drop of test cards
- Manages search and filtering
- Shows/hides the asset panel

**When to modify:**
- Changing workflow stages
- Adding new card actions
- Modifying search behavior

#### `src/components/TestStationSidebar.tsx` - The Navigation Panel
```typescript
// This is like a table of contents for your app
// Shows all available test stations organized by category

const TestStationSidebar: React.FC<TestStationSidebarProps> = ({
  categories,
  onStationSelect,
  onGroupSelect,
  onDashboardSelect
}) => {
  // Track which categories are expanded
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>();
}
```

**What it does:**
- Organizes stations by IEC standards
- Provides collapsible navigation
- Shows station status indicators
- Includes dashboard access button

**When to modify:**
- Adding new station categories
- Changing navigation structure
- Modifying status indicators

### Data Files

#### `src/data/testStations.ts` - Station Definitions
```typescript
// This is like a database of all your test stations
// Defines what stations exist and their properties

export const testStationCategories: TestStationCategory[] = [
  {
    id: 'category-iec26',
    name: 'EMC Testing',
    standard: 'IEC 60255-26',
    stations: [...],  // Array of individual stations
    groups: [...]     // Array of station groups
  }
];
```

**What it does:**
- Defines all test stations in your system
- Organizes them by testing standards
- Creates grouped stations (multiple identical stations)

**When to modify:**
- Adding new test stations
- Creating new station groups
- Changing station properties

#### `src/data/mockData.ts` - Sample Data
```typescript
// This provides example data for development and testing
// Like having sample customers in a demo store

export const mockSwimlanes: Swimlane[] = [
  {
    id: 'swimlane-001',
    title: 'Test Planning',
    color: '#6B7280',
    order: 0
  }
];
```

**What it does:**
- Provides sample workflow stages
- Includes example test cards and assets
- Used for development and demonstrations

**When to modify:**
- Changing default workflow stages
- Adding sample data for new features
- Customizing example content

### Type Definitions

#### `src/types/index.ts` - Data Structure Definitions
```typescript
// This file defines the "shape" of your data
// Like blueprints for building data objects

export interface TestStation {
  id: string;           // Unique identifier
  name: string;         // Display name
  standard: string;     // IEC standard (26 or 27)
  board: KanbanBoard;   // The station's workflow board
  isActive?: boolean;   // Whether station is currently active
}
```

**What it does:**
- Defines data structures for TypeScript
- Ensures data consistency across the app
- Provides autocomplete and error checking

**When to modify:**
- Adding new properties to existing data types
- Creating new data types for new features
- Changing required vs optional fields

---

## 🚀 How to Add New Features

### Adding a New Test Station

**Step 1: Define the Station**
```typescript
// In src/data/testStations.ts
const newStation: TestStation = {
  id: 'station-new-001',
  name: 'My New Test Station',
  standard: 'IEC 60255-26',
  category: 'IEC 60255-26',
  description: 'Description of what this station does',
  board: createBaseBoard('board-new-001', 'My New Test Station'),
  isActive: true,
  stationNumber: 1
};

// Add it to the appropriate category
const updatedCategory = {
  ...existingCategory,
  stations: [...existingCategory.stations, newStation]
};
```

**Step 2: Test It**
1. Save the file
2. Refresh your browser
3. Look for your new station in the sidebar
4. Click it to make sure it works

### Adding a New Workflow Stage (Swimlane)

**Step 1: Add to Mock Data**
```typescript
// In src/data/mockData.ts
export const mockSwimlanes: Swimlane[] = [
  // ... existing swimlanes
  {
    id: 'swimlane-006',
    title: 'Quality Review',  // Your new stage name
    color: '#EC4899',         // Pick a color
    order: 5                  // Position in workflow
  }
];
```

**Step 2: Update Dashboard Logic (if needed)**
```typescript
// In src/components/TestStationDashboard.tsx
// Add logic to categorize cards in your new stage
const qualityReviewCount = station.board.cards.filter(card => 
  card.currentSwimlane === 'swimlane-006'
).length;
```

### Adding a New Asset Type

**Step 1: Add Sample Assets**
```typescript
// In src/data/mockData.ts
export const mockAssets: Asset[] = [
  // ... existing assets
  {
    id: 'asset-008',
    name: 'New Equipment Type',
    type: 'Specialized Testing',
    status: 'available',
    description: 'What this equipment does',
    location: 'Lab C'
  }
];
```

**Step 2: Update Asset Panel (if needed)**
```typescript
// In src/components/AssetPanel.tsx
// Add any special handling for your new asset type
```

---

## 🔧 How to Modify Existing Features

### Changing Colors and Styling

**Method 1: Tailwind Classes**
```typescript
// Find the element you want to change
<div className="bg-blue-500 text-white p-4 rounded-lg">
  Content here
</div>

// Change colors by modifying class names:
// bg-blue-500 → bg-green-500 (background color)
// text-white → text-gray-900 (text color)
// p-4 → p-6 (padding)
```

**Common Tailwind Patterns:**
- **Colors**: `bg-red-500`, `text-blue-600`, `border-gray-200`
- **Spacing**: `p-4` (padding), `m-2` (margin), `gap-3` (space between items)
- **Sizing**: `w-full` (full width), `h-64` (specific height)
- **Layout**: `flex`, `grid`, `items-center`, `justify-between`

**Method 2: Custom Colors**
```typescript
// For swimlane colors, edit the color property directly
{
  id: 'swimlane-001',
  title: 'Test Planning',
  color: '#FF6B6B',  // Change this hex color
  order: 0
}
```

### Modifying Dashboard Statistics

**Step 1: Find the Calculation Logic**
```typescript
// In src/components/TestStationDashboard.tsx
const totalStats = filteredSummaries.reduce((acc, summary) => ({
  totalStations: acc.totalStations + 1,
  activeStations: acc.activeStations + (summary.station.isActive ? 1 : 0),
  // Add your new statistic here:
  newStatistic: acc.newStatistic + calculateNewValue(summary),
}), {
  totalStations: 0,
  activeStations: 0,
  newStatistic: 0  // Initialize your new statistic
});
```

**Step 2: Add Display Element**
```typescript
// Add a new stats card
<div className="bg-purple-50 p-3 rounded-lg text-center">
  <div className="text-2xl font-bold text-purple-800">
    {totalStats.newStatistic}
  </div>
  <div className="text-xs text-purple-600">New Statistic</div>
</div>
```

### Changing Workflow Logic

**Example: Adding Auto-Assignment**
```typescript
// In src/components/KanbanBoard.tsx
const handleCardMove = (swimlaneId: string) => {
  if (!draggedCard) return;

  // Your custom logic here
  let updatedCard = { ...draggedCard, currentSwimlane: swimlaneId };
  
  // Example: Auto-assign when moved to "Testing"
  if (swimlaneId === 'swimlane-003' && !updatedCard.assignedTo) {
    updatedCard.assignedTo = 'current-user';
  }

  // Update the board
  const updatedCards = board.cards.map(card =>
    card.id === draggedCard.id ? updatedCard : card
  );

  onUpdateBoard({ ...board, cards: updatedCards });
};
```

---

## 🎨 Common Customizations

### Changing the App Title and Branding

**Step 1: Update HTML Title**
```html
<!-- In index.html -->
<title>Your Custom App Name</title>
```

**Step 2: Update Component Headers**
```typescript
// In various components, look for titles like:
<h1 className="text-2xl font-bold text-gray-900">
  Your Custom Title Here
</h1>
```

### Adding New Icons

**Step 1: Import from Lucide**
```typescript
// At the top of your component file
import { 
  Activity, 
  Clock, 
  YourNewIcon  // Add your icon here
} from 'lucide-react';
```

**Step 2: Use the Icon**
```typescript
<YourNewIcon className="w-5 h-5 text-gray-500" />
```

**Available Icons**: Browse at [lucide.dev](https://lucide.dev/)

### Customizing the Sidebar

**Adding a New Section**
```typescript
// In src/components/TestStationSidebar.tsx
// Add after existing sections:
<div className="p-4 border-t border-gray-200">
  <h3 className="font-medium text-gray-900 mb-2">Your New Section</h3>
  <button className="w-full p-2 text-left hover:bg-gray-50 rounded">
    New Feature Button
  </button>
</div>
```

**Changing Collapse Behavior**
```typescript
// Modify the collapsed view
if (isCollapsed) {
  return (
    <div className="w-16 bg-white border-r border-gray-200 h-full flex flex-col">
      {/* Add your custom collapsed content */}
    </div>
  );
}
```

---

## 🐛 Debugging Guide

### Common Error Messages

#### "Cannot read property 'X' of undefined"
**What it means**: You're trying to access a property on something that doesn't exist.

**How to fix**:
```typescript
// Instead of:
station.board.cards.length  // Crashes if station is undefined

// Use:
station?.board?.cards?.length || 0  // Safe with optional chaining
```

#### "Type 'X' is not assignable to type 'Y'"
**What it means**: TypeScript found a data type mismatch.

**How to fix**:
1. Check the expected type in `src/types/index.ts`
2. Make sure your data matches exactly
3. Use type assertions if you're sure: `data as ExpectedType`

#### "Module not found"
**What it means**: You're trying to import something that doesn't exist.

**How to fix**:
```typescript
// Check your import paths:
import Component from './Component';     // Same folder
import Component from '../Component';    // Parent folder
import Component from './folder/Component';  // Subfolder
```

### Debugging Techniques

#### 1. Console Logging
```typescript
// Add temporary logging to see what's happening
console.log('Current station:', activeStation);
console.log('Board data:', activeStation?.board);
console.log('Cards:', activeStation?.board?.cards);
```

#### 2. React Developer Tools
1. Install React DevTools browser extension
2. Open browser developer tools (F12)
3. Go to "Components" tab
4. Inspect component state and props

#### 3. Network Tab
1. Open browser developer tools (F12)
2. Go to "Network" tab
3. Look for failed requests (red entries)
4. Check if all assets are loading correctly

### Performance Issues

#### Slow Rendering
**Symptoms**: App feels sluggish, especially when dragging cards

**Solutions**:
```typescript
// Use React.memo for expensive components
const ExpensiveComponent = React.memo(({ data }) => {
  // Component logic
});

// Use useMemo for expensive calculations
const expensiveValue = useMemo(() => {
  return heavyCalculation(data);
}, [data]);
```

#### Memory Leaks
**Symptoms**: App gets slower over time

**Solutions**:
```typescript
// Clean up event listeners in useEffect
useEffect(() => {
  const handleResize = () => { /* logic */ };
  window.addEventListener('resize', handleResize);
  
  // Cleanup function
  return () => {
    window.removeEventListener('resize', handleResize);
  };
}, []);
```

---

## 🚀 Deployment Guide

### Development vs Production

**Development** (what you're doing now):
- Code changes show immediately
- Detailed error messages
- Larger file sizes
- Debug tools available

**Production** (what users see):
- Optimized and compressed code
- Generic error messages
- Smaller file sizes
- No debug tools

### Building for Production

**Step 1: Create Production Build**
```bash
npm run build
```

This creates a `dist/` folder with optimized files.

**Step 2: Test Production Build Locally**
```bash
npm run preview
```

### Deployment Options

#### Option 1: Static Hosting (Easiest)
Services like Netlify, Vercel, or GitHub Pages:

1. **Netlify**:
   - Drag and drop your `dist/` folder to netlify.com
   - Or connect your GitHub repository for automatic deployments

2. **Vercel**:
   - Connect your GitHub repository
   - Automatic deployments on every push

3. **GitHub Pages**:
   - Push your code to GitHub
   - Enable Pages in repository settings

#### Option 2: SharePoint Integration
Since you mentioned SharePoint:

1. **SharePoint Framework (SPFx)**:
   - Requires converting to SPFx web part
   - More complex but fully integrated

2. **Embed as iFrame**:
   - Deploy to static hosting first
   - Embed in SharePoint page using iFrame web part

### Environment Configuration

**Step 1: Create Environment Files**
```bash
# .env.development
VITE_APP_TITLE=Test Station Tracker (Dev)
VITE_API_URL=http://localhost:3000

# .env.production
VITE_APP_TITLE=Test Station Tracker
VITE_API_URL=https://your-api.com
```

**Step 2: Use in Code**
```typescript
const appTitle = import.meta.env.VITE_APP_TITLE;
const apiUrl = import.meta.env.VITE_API_URL;
```

---

## 🎓 Learning Path

### Week 1: Understanding the Basics
- [ ] Run the app and explore all features
- [ ] Read through `App.tsx` and understand the main flow
- [ ] Modify some text and colors to see changes
- [ ] Add a new test station using the guide above

### Week 2: Component Deep Dive
- [ ] Study one component file per day
- [ ] Understand props and state in each component
- [ ] Make small modifications to each component
- [ ] Create a simple new component

### Week 3: Data and Logic
- [ ] Understand the data flow from `testStations.ts` to components
- [ ] Modify the dashboard calculations
- [ ] Add a new asset type
- [ ] Implement a simple new feature

### Week 4: Advanced Features
- [ ] Study the drag-and-drop implementation
- [ ] Add custom filtering logic
- [ ] Implement data persistence (localStorage)
- [ ] Deploy your customized version

### Recommended Resources
1. **React**: [react.dev/learn](https://react.dev/learn)
2. **TypeScript**: [typescriptlang.org/docs](https://www.typescriptlang.org/docs/)
3. **Tailwind CSS**: [tailwindcss.com/docs](https://tailwindcss.com/docs)
4. **JavaScript**: [javascript.info](https://javascript.info/)

---

## 🆘 Getting Help

### Before Asking for Help
1. **Read the error message carefully** - it usually tells you what's wrong
2. **Check the browser console** (F12) for additional error details
3. **Try the debugging techniques** listed above
4. **Search online** - copy/paste error messages into Google

### Where to Get Help
1. **Stack Overflow** - For specific coding questions
2. **React Documentation** - For React-specific issues
3. **GitHub Issues** - For problems with specific libraries
4. **Discord/Reddit** - For general programming help

### How to Ask Good Questions
1. **Describe what you're trying to do**
2. **Show the code that's not working**
3. **Include the full error message**
4. **Explain what you've already tried**

---

**Remember**: Programming is like learning a new language. It takes time and practice, but every small step builds your understanding. Don't be afraid to experiment and break things - that's how you learn! 🚀