import React from 'react';
import { Swimlane as SwimlaneType, DataCard } from '../types';
import { Plus, MoreHorizontal } from 'lucide-react';
import DataCardComponent from './DataCard';

interface SwimlaneProps {
  swimlane: SwimlaneType;
  cards: DataCard[];
  onDragStart: (card: DataCard) => void;
  onDragEnd: () => void;
  onDragOver: (swimlaneId: string) => void;
  onDragLeave: () => void;
  onDrop: (swimlaneId: string) => void;
  isDragOver?: boolean;
  draggedCard?: DataCard | null;
}

const Swimlane: React.FC<SwimlaneProps> = ({
  swimlane,
  cards,
  onDragStart,
  onDragEnd,
  onDragOver,
  onDragLeave,
  onDrop,
  isDragOver = false,
  draggedCard
}) => {
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
    onDragOver(swimlane.id);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    onDrop(swimlane.id);
  };

  return (
    <div className="flex-shrink-0 w-80">
      {/* Swimlane Header */}
      <div className="flex items-center justify-between p-4 bg-gray-50 rounded-t-lg border-b">
        <div className="flex items-center gap-3">
          <div 
            className="w-3 h-3 rounded-full"
            style={{ backgroundColor: swimlane.color }}
          />
          <h2 className="font-semibold text-gray-900">{swimlane.title}</h2>
          <span className="bg-gray-200 text-gray-600 text-xs px-2 py-1 rounded-full">
            {cards.length}
          </span>
        </div>
        
        <div className="flex items-center gap-1">
          <button className="p-1 hover:bg-gray-200 rounded transition-colors">
            <Plus className="w-4 h-4 text-gray-500" />
          </button>
          <button className="p-1 hover:bg-gray-200 rounded transition-colors">
            <MoreHorizontal className="w-4 h-4 text-gray-500" />
          </button>
        </div>
      </div>

      {/* Swimlane Content */}
      <div
        onDragOver={handleDragOver}
        onDragLeave={onDragLeave}
        onDrop={handleDrop}
        className={`
          min-h-96 p-4 bg-gray-25 rounded-b-lg border-l border-r border-b
          transition-all duration-200
          ${isDragOver 
            ? 'bg-blue-50 border-blue-300 border-2 border-dashed' 
            : 'bg-gray-25 border-gray-200'
          }
        `}
      >
        <div className="space-y-3">
          {cards.map((card) => (
            <DataCardComponent
              key={card.id}
              card={card}
              onDragStart={onDragStart}
              onDragEnd={onDragEnd}
              isDragging={draggedCard?.id === card.id}
            />
          ))}
          
          {cards.length === 0 && (
            <div className="text-center py-8 text-gray-400">
              <p className="text-sm">No cards in this swimlane</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Swimlane;