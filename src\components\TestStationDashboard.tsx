import React, { useState } from 'react';
import { TestStationCategory, TestStation, TestStationGroup } from '../types';
import { 
  Activity, 
  Clock, 
  AlertCircle, 
  CheckCircle, 
  TrendingUp,
  Users,
  Calendar,
  Filter,
  Search,
  Grid3X3,
  BarChart3
} from 'lucide-react';

interface TestStationDashboardProps {
  categories: TestStationCategory[];
  onStationSelect: (station: TestStation) => void;
  onGroupSelect: (group: TestStationGroup) => void;
  onBack: () => void;
}

interface StationSummary {
  station: TestStation;
  group?: TestStationGroup;
  category: TestStationCategory;
  pendingCount: number;
  inProgressCount: number;
  completedCount: number;
  totalAssets: number;
  availableAssets: number;
  reservedAssets: number;
}

const TestStationDashboard: React.FC<TestStationDashboardProps> = ({
  categories,
  onStationSelect,
  onGroupSelect,
  onBack
}) => {
  const [filterCategory, setFilterCategory] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [viewMode, setViewMode] = useState<'cards' | 'table'>('cards');

  // Get station summaries
  const getStationSummaries = (): StationSummary[] => {
    const summaries: StationSummary[] = [];
    
    categories.forEach(category => {
      // Add individual stations
      category.stations.forEach(station => {
        const group = category.groups.find(g => g.id === station.groupId);
        
        const pendingCount = station.board.cards.filter(card => 
          card.currentSwimlane === 'swimlane-001' || card.currentSwimlane === 'swimlane-002'
        ).length;
        
        const inProgressCount = station.board.cards.filter(card => 
          card.currentSwimlane === 'swimlane-003'
        ).length;
        
        const completedCount = station.board.cards.filter(card => 
          card.currentSwimlane === 'swimlane-004' || card.currentSwimlane === 'swimlane-005'
        ).length;

        const availableAssets = station.board.assets.filter(a => a.status === 'available').length;
        const reservedAssets = station.board.assets.filter(a => a.status === 'reserved').length;

        summaries.push({
          station,
          group,
          category,
          pendingCount,
          inProgressCount,
          completedCount,
          totalAssets: station.board.assets.length,
          availableAssets,
          reservedAssets
        });
      });
    });

    return summaries;
  };

  const stationSummaries = getStationSummaries();

  // Filter summaries
  const filteredSummaries = stationSummaries.filter(summary => {
    const matchesCategory = filterCategory === 'all' || summary.category.id === filterCategory;
    const matchesSearch = searchTerm === '' || 
      summary.station.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      summary.group?.name.toLowerCase().includes(searchTerm.toLowerCase());
    
    return matchesCategory && matchesSearch;
  });

  // Calculate totals
  const totalStats = filteredSummaries.reduce((acc, summary) => ({
    totalStations: acc.totalStations + 1,
    activeStations: acc.activeStations + (summary.station.isActive ? 1 : 0),
    totalPending: acc.totalPending + summary.pendingCount,
    totalInProgress: acc.totalInProgress + summary.inProgressCount,
    totalCompleted: acc.totalCompleted + summary.completedCount,
    totalAssets: acc.totalAssets + summary.totalAssets,
    availableAssets: acc.availableAssets + summary.availableAssets,
    reservedAssets: acc.reservedAssets + summary.reservedAssets
  }), {
    totalStations: 0,
    activeStations: 0,
    totalPending: 0,
    totalInProgress: 0,
    totalCompleted: 0,
    totalAssets: 0,
    availableAssets: 0,
    reservedAssets: 0
  });

  const getStatusColor = (pendingCount: number, inProgressCount: number) => {
    if (inProgressCount > 0) return 'border-l-blue-500 bg-blue-50';
    if (pendingCount > 0) return 'border-l-yellow-500 bg-yellow-50';
    return 'border-l-green-500 bg-green-50';
  };

  const getStatusIcon = (pendingCount: number, inProgressCount: number) => {
    if (inProgressCount > 0) return <Activity className="w-5 h-5 text-blue-600" />;
    if (pendingCount > 0) return <Clock className="w-5 h-5 text-yellow-600" />;
    return <CheckCircle className="w-5 h-5 text-green-600" />;
  };

  const getStatusText = (pendingCount: number, inProgressCount: number) => {
    if (inProgressCount > 0) return 'Testing Active';
    if (pendingCount > 0) return 'Tests Pending';
    return 'Available';
  };

  return (
    <div className="h-screen bg-gray-50 flex flex-col">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Test Station Dashboard</h1>
            <p className="text-sm text-gray-600">Overview of all test stations and their current status</p>
          </div>
          
          <div className="flex items-center gap-3">
            <button
              onClick={() => setViewMode(viewMode === 'cards' ? 'table' : 'cards')}
              className="px-4 py-2 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors flex items-center gap-2"
            >
              {viewMode === 'cards' ? <BarChart3 className="w-4 h-4" /> : <Grid3X3 className="w-4 h-4" />}
              {viewMode === 'cards' ? 'Table View' : 'Card View'}
            </button>
            <button
              onClick={onBack}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Back to Stations
            </button>
          </div>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4 mb-4">
          <div className="bg-blue-50 p-3 rounded-lg text-center">
            <div className="text-2xl font-bold text-blue-800">{totalStats.totalStations}</div>
            <div className="text-xs text-blue-600">Total Stations</div>
          </div>
          <div className="bg-green-50 p-3 rounded-lg text-center">
            <div className="text-2xl font-bold text-green-800">{totalStats.activeStations}</div>
            <div className="text-xs text-green-600">Active Stations</div>
          </div>
          <div className="bg-yellow-50 p-3 rounded-lg text-center">
            <div className="text-2xl font-bold text-yellow-800">{totalStats.totalPending}</div>
            <div className="text-xs text-yellow-600">Tests Pending</div>
          </div>
          <div className="bg-blue-50 p-3 rounded-lg text-center">
            <div className="text-2xl font-bold text-blue-800">{totalStats.totalInProgress}</div>
            <div className="text-xs text-blue-600">Tests In Progress</div>
          </div>
          <div className="bg-green-50 p-3 rounded-lg text-center">
            <div className="text-2xl font-bold text-green-800">{totalStats.totalCompleted}</div>
            <div className="text-xs text-green-600">Tests Completed</div>
          </div>
          <div className="bg-gray-50 p-3 rounded-lg text-center">
            <div className="text-2xl font-bold text-gray-800">{totalStats.totalAssets}</div>
            <div className="text-xs text-gray-600">Total Assets</div>
          </div>
          <div className="bg-green-50 p-3 rounded-lg text-center">
            <div className="text-2xl font-bold text-green-800">{totalStats.availableAssets}</div>
            <div className="text-xs text-green-600">Available Assets</div>
          </div>
          <div className="bg-red-50 p-3 rounded-lg text-center">
            <div className="text-2xl font-bold text-red-800">{totalStats.reservedAssets}</div>
            <div className="text-xs text-red-600">Reserved Assets</div>
          </div>
        </div>

        {/* Filters */}
        <div className="flex items-center gap-4">
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search stations or groups..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          
          <div className="flex items-center gap-2">
            <Filter className="w-4 h-4 text-gray-400" />
            <select
              value={filterCategory}
              onChange={(e) => setFilterCategory(e.target.value)}
              className="px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">All Categories</option>
              {categories.map(category => (
                <option key={category.id} value={category.id}>
                  {category.name} ({category.standard})
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-auto p-6">
        {viewMode === 'cards' ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {filteredSummaries.map((summary) => (
              <div
                key={summary.station.id}
                className={`
                  bg-white rounded-lg border-l-4 shadow-sm hover:shadow-md transition-all duration-200 cursor-pointer
                  ${getStatusColor(summary.pendingCount, summary.inProgressCount)}
                `}
                onClick={() => onStationSelect(summary.station)}
              >
                <div className="p-4">
                  {/* Header */}
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1">
                      <h3 className="font-semibold text-gray-900 text-sm mb-1">
                        {summary.station.name}
                      </h3>
                      {summary.group && (
                        <p className="text-xs text-gray-600 mb-1">
                          {summary.group.name}
                        </p>
                      )}
                      <p className="text-xs text-gray-500">
                        {summary.category.standard}
                      </p>
                    </div>
                    <div className="flex items-center gap-2">
                      {getStatusIcon(summary.pendingCount, summary.inProgressCount)}
                      {summary.station.isActive && (
                        <div className="w-2 h-2 bg-green-500 rounded-full" />
                      )}
                    </div>
                  </div>

                  {/* Status */}
                  <div className="mb-3">
                    <span className="text-xs font-medium text-gray-700">
                      {getStatusText(summary.pendingCount, summary.inProgressCount)}
                    </span>
                  </div>

                  {/* Stats Grid */}
                  <div className="grid grid-cols-2 gap-3 mb-3">
                    <div className="text-center p-2 bg-yellow-50 rounded">
                      <div className="text-lg font-bold text-yellow-800">
                        {summary.pendingCount}
                      </div>
                      <div className="text-xs text-yellow-600">Pending</div>
                    </div>
                    <div className="text-center p-2 bg-blue-50 rounded">
                      <div className="text-lg font-bold text-blue-800">
                        {summary.inProgressCount}
                      </div>
                      <div className="text-xs text-blue-600">In Progress</div>
                    </div>
                  </div>

                  {/* Assets */}
                  <div className="flex items-center justify-between text-xs text-gray-600">
                    <span>{summary.availableAssets} available assets</span>
                    <span>{summary.reservedAssets} reserved</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="bg-white rounded-lg shadow-sm overflow-hidden">
            <table className="w-full">
              <thead className="bg-gray-50 border-b border-gray-200">
                <tr>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Station
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Group
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Pending
                  </th>
                  <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    In Progress
                  </th>
                  <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Completed
                  </th>
                  <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Assets
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredSummaries.map((summary) => (
                  <tr
                    key={summary.station.id}
                    onClick={() => onStationSelect(summary.station)}
                    className="hover:bg-gray-50 cursor-pointer"
                  >
                    <td className="px-4 py-3">
                      <div className="flex items-center gap-3">
                        {getStatusIcon(summary.pendingCount, summary.inProgressCount)}
                        <div>
                          <div className="font-medium text-gray-900 text-sm">
                            {summary.station.name}
                          </div>
                          <div className="text-xs text-gray-500">
                            {summary.category.standard}
                          </div>
                        </div>
                        {summary.station.isActive && (
                          <div className="w-2 h-2 bg-green-500 rounded-full" />
                        )}
                      </div>
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-600">
                      {summary.group?.name || '-'}
                    </td>
                    <td className="px-4 py-3">
                      <span className="text-sm text-gray-700">
                        {getStatusText(summary.pendingCount, summary.inProgressCount)}
                      </span>
                    </td>
                    <td className="px-4 py-3 text-center">
                      <span className={`
                        inline-flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium
                        ${summary.pendingCount > 0 
                          ? 'bg-yellow-100 text-yellow-800' 
                          : 'bg-gray-100 text-gray-500'
                        }
                      `}>
                        {summary.pendingCount}
                      </span>
                    </td>
                    <td className="px-4 py-3 text-center">
                      <span className={`
                        inline-flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium
                        ${summary.inProgressCount > 0 
                          ? 'bg-blue-100 text-blue-800' 
                          : 'bg-gray-100 text-gray-500'
                        }
                      `}>
                        {summary.inProgressCount}
                      </span>
                    </td>
                    <td className="px-4 py-3 text-center">
                      <span className={`
                        inline-flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium
                        ${summary.completedCount > 0 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-gray-100 text-gray-500'
                        }
                      `}>
                        {summary.completedCount}
                      </span>
                    </td>
                    <td className="px-4 py-3 text-center text-sm text-gray-600">
                      <div className="flex items-center justify-center gap-2">
                        <span className="text-green-600">{summary.availableAssets}</span>
                        <span className="text-gray-400">/</span>
                        <span className="text-red-600">{summary.reservedAssets}</span>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}

        {filteredSummaries.length === 0 && (
          <div className="text-center py-12">
            <div className="text-gray-400 mb-2">
              <Activity className="w-12 h-12 mx-auto" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-1">No stations found</h3>
            <p className="text-gray-500">Try adjusting your search or filter criteria.</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default TestStationDashboard;