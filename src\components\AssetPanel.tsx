import React, { useState } from 'react';
import { Asset } from '../types';
import { Search, Filter, Plus } from 'lucide-react';
import AssetBadge from './AssetBadge';

interface AssetPanelProps {
  assets: Asset[];
  onAssetSelect?: (asset: Asset) => void;
}

const AssetPanel: React.FC<AssetPanelProps> = ({ assets, onAssetSelect }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<Asset['status'] | 'all'>('all');

  const filteredAssets = assets.filter(asset => {
    const matchesSearch = asset.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         asset.type.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = filterStatus === 'all' || asset.status === filterStatus;
    return matchesSearch && matchesFilter;
  });

  const availableAssets = assets.filter(asset => asset.status === 'available').length;
  const reservedAssets = assets.filter(asset => asset.status === 'reserved').length;
  const maintenanceAssets = assets.filter(asset => asset.status === 'maintenance').length;

  return (
    <div className="w-80 bg-white border-l border-gray-200 h-full overflow-hidden flex flex-col">
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-gray-900">Assets</h2>
          <button className="p-2 hover:bg-gray-100 rounded-lg transition-colors">
            <Plus className="w-4 h-4 text-gray-500" />
          </button>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-3 gap-2 mb-4">
          <div className="text-center p-2 bg-green-50 rounded-lg">
            <div className="text-lg font-semibold text-green-800">{availableAssets}</div>
            <div className="text-xs text-green-600">Available</div>
          </div>
          <div className="text-center p-2 bg-yellow-50 rounded-lg">
            <div className="text-lg font-semibold text-yellow-800">{reservedAssets}</div>
            <div className="text-xs text-yellow-600">Reserved</div>
          </div>
          <div className="text-center p-2 bg-red-50 rounded-lg">
            <div className="text-lg font-semibold text-red-800">{maintenanceAssets}</div>
            <div className="text-xs text-red-600">Maintenance</div>
          </div>
        </div>

        {/* Search */}
        <div className="relative mb-3">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search assets..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        {/* Filter */}
        <div className="flex items-center gap-2">
          <Filter className="w-4 h-4 text-gray-400" />
          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value as Asset['status'] | 'all')}
            className="flex-1 px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="all">All Status</option>
            <option value="available">Available</option>
            <option value="reserved">Reserved</option>
            <option value="maintenance">Maintenance</option>
          </select>
        </div>
      </div>

      {/* Asset List */}
      <div className="flex-1 overflow-y-auto p-4">
        <div className="space-y-3">
          {filteredAssets.map((asset) => (
            <div
              key={asset.id}
              onClick={() => onAssetSelect?.(asset)}
              className="p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
            >
              <div className="flex items-start justify-between mb-2">
                <h3 className="font-medium text-gray-900 text-sm">{asset.name}</h3>
                <AssetBadge asset={asset} size="sm" showStatus={true} />
              </div>
              <p className="text-xs text-gray-600 mb-1">{asset.type}</p>
              {asset.description && (
                <p className="text-xs text-gray-500 mb-2">{asset.description}</p>
              )}
              {asset.location && (
                <p className="text-xs text-gray-400">📍 {asset.location}</p>
              )}
            </div>
          ))}
        </div>

        {filteredAssets.length === 0 && (
          <div className="text-center py-8 text-gray-400">
            <p className="text-sm">No assets found</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default AssetPanel;